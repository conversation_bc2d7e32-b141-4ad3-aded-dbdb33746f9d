'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { XMarkIcon, Cog6ToothIcon, CloudArrowDownIcon } from '@heroicons/react/24/outline';
import { WorkflowNode, ProviderNodeData, VisionNodeData, RoleAgentNodeData, CentralRouterNodeData, ToolNodeData, PlannerNodeData, BrowsingNodeData, MemoryNodeData } from '@/types/manualBuild';
import { llmProviders } from '@/config/models';
import { PREDEFINED_ROLES, type Role } from '@/config/roles';

interface NodeConfigPanelProps {
  node: WorkflowNode;
  onUpdate: (updates: Partial<WorkflowNode['data']>) => void;
  onClose: () => void;
}

const PROVIDER_OPTIONS = llmProviders.map(p => ({ value: p.id, label: p.name }));

interface ModelInfo {
  id: string;
  name: string;
  display_name?: string;
  provider_id: string;
  modality?: string;
  context_window?: number;
  input_token_limit?: number;
  output_token_limit?: number;
}

export default function NodeConfigPanel({ node, onUpdate, onClose }: NodeConfigPanelProps) {
  const [config, setConfig] = useState(node.data.config);
  const [fetchedProviderModels, setFetchedProviderModels] = useState<ModelInfo[] | null>(null);
  const [isFetchingProviderModels, setIsFetchingProviderModels] = useState(false);
  const [fetchProviderModelsError, setFetchProviderModelsError] = useState<string | null>(null);

  // Role management state
  const [customRoles, setCustomRoles] = useState<Array<{
    id: string;
    role_id: string;
    name: string;
    description?: string;
    user_id: string;
    created_at: string;
    updated_at: string;
  }>>([]);
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const [rolesError, setRolesError] = useState<string | null>(null);

  // Fetch models from database
  const fetchModelsFromDatabase = useCallback(async () => {
    setIsFetchingProviderModels(true);
    setFetchProviderModelsError(null);
    setFetchedProviderModels(null);
    try {
      const response = await fetch('/api/providers/list-models', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch models from database.');
      }
      if (data.models) {
        setFetchedProviderModels(data.models);
      } else {
        setFetchedProviderModels([]);
      }
    } catch (err: any) {
      console.error('Error fetching models:', err);
      setFetchProviderModelsError(err.message);
      setFetchedProviderModels([]);
    } finally {
      setIsFetchingProviderModels(false);
    }
  }, []);

  // Fetch custom roles from database
  const fetchCustomRoles = useCallback(async () => {
    setIsLoadingRoles(true);
    setRolesError(null);

    try {
      const response = await fetch('/api/user/custom-roles');
      if (!response.ok) {
        throw new Error('Failed to fetch custom roles');
      }
      const roles = await response.json();
      setCustomRoles(roles);
    } catch (err: any) {
      console.error('Error fetching custom roles:', err);
      setRolesError(err.message);
      setCustomRoles([]);
    } finally {
      setIsLoadingRoles(false);
    }
  }, []);

  // Load models and roles on component mount
  useEffect(() => {
    if (node.type === 'provider' || node.type === 'vision') {
      fetchModelsFromDatabase();
    }
    if (node.type === 'roleAgent') {
      fetchCustomRoles();
    }
  }, [node.type, fetchModelsFromDatabase, fetchCustomRoles]);

  // Auto-select first model when provider changes or models load
  useEffect(() => {
    if ((node.type === 'provider' || node.type === 'vision') && fetchedProviderModels && fetchedProviderModels.length > 0) {
      const providerConfig = config as ProviderNodeData['config'] | VisionNodeData['config'];
      const currentProviderDetails = llmProviders.find(p => p.id === providerConfig.providerId);

      if (currentProviderDetails && providerConfig.providerId && !providerConfig.modelId) {
        let availableModels: { value: string; label: string; provider_id?: string; }[] = [];

        if (currentProviderDetails.id === "openrouter") {
          availableModels = fetchedProviderModels
            .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
            .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
        } else if (currentProviderDetails.id === "deepseek") {
          const deepseekChatModel = fetchedProviderModels.find(
            (model) => model.id === "deepseek-chat" && model.provider_id === "deepseek"
          );
          if (deepseekChatModel) {
            availableModels.push({
              value: "deepseek-chat",
              label: "Deepseek V3",
              provider_id: "deepseek",
            });
          }
          const deepseekReasonerModel = fetchedProviderModels.find(
            (model) => model.id === "deepseek-reasoner" && model.provider_id === "deepseek"
          );
          if (deepseekReasonerModel) {
            availableModels.push({
              value: "deepseek-reasoner",
              label: "DeepSeek R1-0528",
              provider_id: "deepseek",
            });
          }
        } else {
          availableModels = fetchedProviderModels
            .filter(model => model.provider_id === currentProviderDetails.id)
            .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
            .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
        }

        if (availableModels.length > 0) {
          const selectedModelId = availableModels[0].value;
          const selectedModel = fetchedProviderModels.find(m => m.id === selectedModelId);

          // Set reasonable default for maxTokens based on model limits
          const defaultMaxTokens = selectedModel?.output_token_limit || selectedModel?.context_window || 4096;
          const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));

          const currentParams = providerConfig.parameters || {};

          // Update config in a single call to avoid infinite loops
          const newConfig = {
            ...providerConfig,
            modelId: selectedModelId,
            parameters: {
              ...currentParams,
              maxTokens: currentParams.maxTokens || reasonableDefault
            }
          };

          setConfig(newConfig);
          onUpdate({
            config: newConfig,
            isConfigured: isNodeConfigured(node.type, newConfig)
          });
        }
      }
    }
  }, [fetchedProviderModels, node.type, (config as ProviderNodeData['config'])?.providerId]); // Only re-run when provider changes

  const handleConfigChange = (key: string, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    onUpdate({
      config: newConfig,
      isConfigured: isNodeConfigured(node.type, newConfig)
    });
  };

  const handleProviderConfigChange = (key: string, value: any) => {
    const currentConfig = config as ProviderNodeData['config'];
    const newConfig = {
      ...currentConfig,
      [key]: value
    };

    // Only initialize parameters if they don't exist and we're setting a parameter
    if (key === 'parameters' || !currentConfig.parameters) {
      newConfig.parameters = {
        temperature: 1.0,
        maxTokens: undefined,
        topP: undefined,
        frequencyPenalty: undefined,
        presencePenalty: undefined,
        ...currentConfig.parameters,
        ...(key === 'parameters' ? value : {})
      };
    }

    setConfig(newConfig);
    onUpdate({
      config: newConfig,
      isConfigured: isNodeConfigured(node.type, newConfig)
    });
  };

  // Model options based on selected provider and fetched models
  const modelOptions = useMemo(() => {
    if (fetchedProviderModels && (node.type === 'provider' || node.type === 'vision')) {
      const providerConfig = config as ProviderNodeData['config'] | VisionNodeData['config'];

      const currentProviderDetails = llmProviders.find(p => p.id === providerConfig.providerId);
      if (!currentProviderDetails) {
        return [];
      }

      // Filter function for vision nodes - only show multimodal models
      const filterForVision = (models: any[]) => {
        if (node.type === 'vision') {
          return models.filter(model =>
            model.modality &&
            (model.modality.includes('multimodal') ||
             model.modality.includes('vision') ||
             model.modality.includes('image'))
          );
        }
        return models;
      };

      // If the selected provider is "OpenRouter", show all fetched models (filtered for vision if needed)
      if (currentProviderDetails.id === "openrouter") {
        const filteredModels = filterForVision(fetchedProviderModels);
        return filteredModels
          .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
          .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
      }

      // Custom logic for DeepSeek
      if (currentProviderDetails.id === "deepseek") {
        const deepseekOptions: { value: string; label: string; provider_id?: string; }[] = [];
        const deepseekChatModel = fetchedProviderModels.find(
          (model) => model.id === "deepseek-chat" && model.provider_id === "deepseek"
        );
        if (deepseekChatModel && (node.type === 'provider' || (node.type === 'vision' && deepseekChatModel.modality?.includes('multimodal')))) {
          deepseekOptions.push({
            value: "deepseek-chat",
            label: "Deepseek V3",
            provider_id: "deepseek",
          });
        }
        const deepseekReasonerModel = fetchedProviderModels.find(
          (model) => model.id === "deepseek-reasoner" && model.provider_id === "deepseek"
        );
        if (deepseekReasonerModel && (node.type === 'provider' || (node.type === 'vision' && deepseekReasonerModel.modality?.includes('multimodal')))) {
          deepseekOptions.push({
            value: "deepseek-reasoner",
            label: "DeepSeek R1-0528",
            provider_id: "deepseek",
          });
        }
        return deepseekOptions.sort((a, b) => (a.label || '').localeCompare(b.label || ''));
      }

      // For other providers, filter by their specific provider_id and vision capabilities
      const providerModels = fetchedProviderModels.filter(model => model.provider_id === currentProviderDetails.id);
      const filteredModels = filterForVision(providerModels);
      return filteredModels
        .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
        .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
    }
    return [];
  }, [fetchedProviderModels, config, node.type]);

  // Get current model's token limits
  const getCurrentModelLimits = useMemo(() => {
    if (!fetchedProviderModels || (node.type !== 'provider' && node.type !== 'vision')) {
      return { maxTokens: 4096, minTokens: 1 }; // Default fallback
    }

    const providerConfig = config as ProviderNodeData['config'] | VisionNodeData['config'];
    if (!providerConfig?.modelId) {
      return { maxTokens: 4096, minTokens: 1 }; // Default when no model selected
    }

    const currentModel = fetchedProviderModels.find(m => m.id === providerConfig.modelId);
    if (!currentModel) {
      return { maxTokens: 4096, minTokens: 1 }; // Default when model not found
    }

    // Use output_token_limit if available, otherwise context_window, otherwise default
    const maxTokens = currentModel.output_token_limit || currentModel.context_window || 4096;
    const minTokens = 1;

    return { maxTokens, minTokens };
  }, [fetchedProviderModels, config, node.type]);

  const isNodeConfigured = (nodeType: string, nodeConfig: any): boolean => {
    switch (nodeType) {
      case 'provider':
        return !!(nodeConfig.providerId && nodeConfig.modelId);
      case 'vision':
        return !!(nodeConfig.providerId && nodeConfig.modelId);
      case 'roleAgent':
        if (nodeConfig.roleType === 'new') {
          return !!(nodeConfig.newRoleName && nodeConfig.customPrompt);
        }
        return !!(nodeConfig.roleId && nodeConfig.roleName);
      case 'centralRouter':
        return !!(nodeConfig.routingStrategy);
      case 'conditional':
        return !!(nodeConfig.condition && nodeConfig.conditionType);
      case 'tool':
        return !!(nodeConfig.toolType);
      case 'planner':
        return !!(nodeConfig.providerId && nodeConfig.modelId);
      case 'browsing':
        return true; // Browsing node is always configured with defaults
      case 'memory':
        return !!(nodeConfig.memoryType && nodeConfig.storageKey);
      case 'switch':
        return !!(nodeConfig.switchType && nodeConfig.cases?.length > 0);
      case 'loop':
        return !!(nodeConfig.loopType);
      default:
        return true;
    }
  };

  const renderProviderConfig = () => {
    const providerConfig = config as ProviderNodeData['config'];

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Provider
          </label>
          <select
            value={providerConfig?.providerId || ''}
            onChange={(e) => {
              const currentConfig = config as ProviderNodeData['config'];
              const newConfig = {
                ...currentConfig,
                providerId: e.target.value as any,
                modelId: '', // Reset model when provider changes
                parameters: currentConfig.parameters || {
                  temperature: 1.0,
                  maxTokens: undefined,
                  topP: undefined,
                  frequencyPenalty: undefined,
                  presencePenalty: undefined,
                }
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
          >
            <option value="">Select Provider</option>
            {PROVIDER_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            API Key
          </label>
          <input
            type="password"
            value={providerConfig?.apiKey || ''}
            onChange={(e) => handleProviderConfigChange('apiKey', e.target.value)}
            placeholder="Enter your API key"
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]"
          />
          {isFetchingProviderModels && fetchedProviderModels === null && (
            <p className="mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg">
              <CloudArrowDownIcon className="h-4 w-4 mr-1 animate-pulse" />
              Fetching models...
            </p>
          )}
          {fetchProviderModelsError && (
            <p className="mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg">
              Error: {fetchProviderModelsError}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Model Variant
          </label>
          <select
            value={providerConfig?.modelId || ''}
            onChange={(e) => {
              const selectedModelId = e.target.value;

              // Update maxTokens based on the selected model
              let updatedConfig = { ...providerConfig, modelId: selectedModelId };

              if (selectedModelId && fetchedProviderModels) {
                const selectedModel = fetchedProviderModels.find(m => m.id === selectedModelId);
                if (selectedModel) {
                  const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;
                  const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));

                  const currentParams = providerConfig?.parameters || {};
                  updatedConfig = {
                    ...updatedConfig,
                    parameters: {
                      ...currentParams,
                      maxTokens: reasonableDefault
                    }
                  };
                }
              }

              // Single state update to avoid infinite loops
              setConfig(updatedConfig);
              onUpdate({
                config: updatedConfig,
                isConfigured: isNodeConfigured(node.type, updatedConfig)
              });
            }}
            disabled={!providerConfig?.providerId || !modelOptions.length}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30"
          >
            {!providerConfig?.providerId ? (
              <option value="" disabled>Select a provider first</option>
            ) : modelOptions.length > 0 ? (
              <>
                <option value="">Select Model</option>
                {modelOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </>
            ) : (
              <option value="" disabled>
                {isFetchingProviderModels ? 'Loading models...' : 'No models available'}
              </option>
            )}
          </select>
        </div>

        <div>
          <label htmlFor="temperature" className="block text-sm font-medium text-gray-300 mb-2">
            Temperature
            <span className="text-xs text-gray-400 ml-1">(0.0 - 2.0)</span>
          </label>
          <div className="space-y-2">
            <input
              type="range"
              id="temperature"
              min="0"
              max="2"
              step="0.1"
              value={providerConfig?.parameters?.temperature || 1.0}
              onChange={(e) => {
                const temp = parseFloat(e.target.value);
                const currentParams = providerConfig?.parameters || {};
                handleProviderConfigChange('parameters', {
                  ...currentParams,
                  temperature: temp
                });
              }}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"
            />
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-400">Conservative</span>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min="0"
                  max="2"
                  step="0.1"
                  value={providerConfig?.parameters?.temperature || 1.0}
                  onChange={(e) => {
                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));
                    const currentParams = providerConfig?.parameters || {};
                    handleProviderConfigChange('parameters', {
                      ...currentParams,
                      temperature: temp
                    });
                  }}
                  className="w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"
                />
              </div>
              <span className="text-xs text-gray-400">Creative</span>
            </div>
            <p className="text-xs text-gray-400">
              Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative
            </p>
          </div>
        </div>

        <div>
          <label htmlFor="maxTokens" className="block text-sm font-medium text-gray-300 mb-2">
            Max Tokens
            <span className="text-xs text-gray-400 ml-1">
              ({getCurrentModelLimits.minTokens} - {getCurrentModelLimits.maxTokens.toLocaleString()})
            </span>
          </label>
          <div className="space-y-2">
            <input
              type="range"
              id="maxTokens"
              min={getCurrentModelLimits.minTokens}
              max={getCurrentModelLimits.maxTokens}
              step="1"
              value={providerConfig?.parameters?.maxTokens || getCurrentModelLimits.maxTokens}
              onChange={(e) => {
                const value = parseInt(e.target.value);
                const currentParams = providerConfig?.parameters || {};
                handleProviderConfigChange('parameters', {
                  ...currentParams,
                  maxTokens: value
                });
              }}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"
            />
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-400">Minimal</span>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min={getCurrentModelLimits.minTokens}
                  max={getCurrentModelLimits.maxTokens}
                  step="1"
                  value={providerConfig?.parameters?.maxTokens || getCurrentModelLimits.maxTokens}
                  onChange={(e) => {
                    const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));
                    const currentParams = providerConfig?.parameters || {};
                    handleProviderConfigChange('parameters', {
                      ...currentParams,
                      maxTokens: value
                    });
                  }}
                  className="w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"
                />
                <button
                  type="button"
                  onClick={() => {
                    const currentParams = providerConfig?.parameters || {};
                    handleProviderConfigChange('parameters', {
                      ...currentParams,
                      maxTokens: getCurrentModelLimits.maxTokens
                    });
                  }}
                  className="text-xs text-orange-400 hover:text-orange-300 underline"
                >
                  Max
                </button>
              </div>
              <span className="text-xs text-gray-400">Maximum</span>
            </div>
            <p className="text-xs text-gray-400">
              Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more.
            </p>
          </div>
        </div>

        {providerConfig?.providerId === 'openrouter' && (
          <div className="p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg">
            <div className="text-sm text-blue-300 font-medium mb-1">🌐 OpenRouter</div>
            <div className="text-xs text-blue-200">
              Access to 300+ models from multiple providers with a single API key.
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderVisionConfig = () => {
    const visionConfig = config as VisionNodeData['config'];

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Provider
          </label>
          <select
            value={visionConfig?.providerId || ''}
            onChange={(e) => {
              const currentConfig = config as VisionNodeData['config'];
              const newConfig = {
                ...currentConfig,
                providerId: e.target.value as any,
                modelId: '', // Reset model when provider changes
                parameters: currentConfig.parameters || {
                  temperature: 1.0,
                  maxTokens: undefined,
                  topP: undefined,
                  frequencyPenalty: undefined,
                  presencePenalty: undefined,
                }
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
          >
            <option value="">Select Provider</option>
            {PROVIDER_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            API Key
          </label>
          <input
            type="password"
            value={visionConfig?.apiKey || ''}
            onChange={(e) => handleProviderConfigChange('apiKey', e.target.value)}
            placeholder="Enter your API key"
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]"
          />
          {isFetchingProviderModels && fetchedProviderModels === null && (
            <p className="mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg">
              <CloudArrowDownIcon className="w-4 h-4 mr-2" />
              Fetching models...
            </p>
          )}
          {fetchProviderModelsError && (
            <p className="mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg">
              Error: {fetchProviderModelsError}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Vision Model
            <span className="text-xs text-purple-400 ml-1">(Multimodal Only)</span>
          </label>
          <select
            value={visionConfig?.modelId || ''}
            onChange={(e) => {
              const selectedModelId = e.target.value;

              // Update maxTokens based on the selected model
              let updatedConfig = { ...visionConfig, modelId: selectedModelId };

              if (selectedModelId && fetchedProviderModels) {
                const selectedModel = fetchedProviderModels.find(m => m.id === selectedModelId);
                if (selectedModel) {
                  const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;
                  const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));

                  const currentParams = visionConfig?.parameters || {};
                  updatedConfig = {
                    ...updatedConfig,
                    parameters: {
                      ...currentParams,
                      maxTokens: reasonableDefault
                    }
                  };
                }
              }

              // Single state update to avoid infinite loops
              setConfig(updatedConfig);
              onUpdate({
                config: updatedConfig,
                isConfigured: isNodeConfigured(node.type, updatedConfig)
              });
            }}
            disabled={!visionConfig?.providerId || !modelOptions.length}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30"
          >
            {!visionConfig?.providerId ? (
              <option value="" disabled>Select a provider first</option>
            ) : modelOptions.length > 0 ? (
              <>
                <option value="">Select Vision Model</option>
                {modelOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </>
            ) : (
              <option value="" disabled>
                {isFetchingProviderModels ? 'Loading models...' : 'No vision models available'}
              </option>
            )}
          </select>
          {modelOptions.length === 0 && visionConfig?.providerId && !isFetchingProviderModels && (
            <p className="mt-2 text-xs text-yellow-400 bg-yellow-900/20 p-2 rounded-lg">
              ⚠️ No multimodal models found for this provider. Vision nodes require models with image processing capabilities.
            </p>
          )}
        </div>

        {/* Temperature and Max Tokens controls - same as Provider node */}
        <div>
          <label htmlFor="temperature" className="block text-sm font-medium text-gray-300 mb-2">
            Temperature (0.0 - 2.0)
          </label>
          <div className="space-y-2">
            <input
              type="range"
              id="temperature"
              min="0"
              max="2"
              step="0.1"
              value={visionConfig?.parameters?.temperature || 1.0}
              onChange={(e) => {
                const temp = parseFloat(e.target.value);
                const currentParams = visionConfig?.parameters || {};
                handleProviderConfigChange('parameters', {
                  ...currentParams,
                  temperature: temp
                });
              }}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"
            />
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-400">Conservative</span>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min="0"
                  max="2"
                  step="0.1"
                  value={visionConfig?.parameters?.temperature || 1.0}
                  onChange={(e) => {
                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));
                    const currentParams = visionConfig?.parameters || {};
                    handleProviderConfigChange('parameters', {
                      ...currentParams,
                      temperature: temp
                    });
                  }}
                  className="w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"
                />
              </div>
              <span className="text-xs text-gray-400">Creative</span>
            </div>
            <p className="text-xs text-gray-400">
              Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative
            </p>
          </div>
        </div>

        <div>
          <label htmlFor="maxTokens" className="block text-sm font-medium text-gray-300 mb-2">
            Max Tokens
            <span className="text-xs text-gray-400 ml-1">
              ({getCurrentModelLimits.minTokens} - {getCurrentModelLimits.maxTokens.toLocaleString()})
            </span>
          </label>
          <div className="space-y-2">
            <input
              type="range"
              id="maxTokens"
              min={getCurrentModelLimits.minTokens}
              max={getCurrentModelLimits.maxTokens}
              step="1"
              value={visionConfig?.parameters?.maxTokens || getCurrentModelLimits.maxTokens}
              onChange={(e) => {
                const value = parseInt(e.target.value);
                const currentParams = visionConfig?.parameters || {};
                handleProviderConfigChange('parameters', {
                  ...currentParams,
                  maxTokens: value
                });
              }}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"
            />
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-400">Minimal</span>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min={getCurrentModelLimits.minTokens}
                  max={getCurrentModelLimits.maxTokens}
                  step="1"
                  value={visionConfig?.parameters?.maxTokens || getCurrentModelLimits.maxTokens}
                  onChange={(e) => {
                    const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));
                    const currentParams = visionConfig?.parameters || {};
                    handleProviderConfigChange('parameters', {
                      ...currentParams,
                      maxTokens: value
                    });
                  }}
                  className="w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"
                />
                <button
                  type="button"
                  onClick={() => {
                    const currentParams = visionConfig?.parameters || {};
                    handleProviderConfigChange('parameters', {
                      ...currentParams,
                      maxTokens: getCurrentModelLimits.maxTokens
                    });
                  }}
                  className="text-xs text-orange-400 hover:text-orange-300 underline"
                >
                  Max
                </button>
              </div>
              <span className="text-xs text-gray-400">Maximum</span>
            </div>
            <p className="text-xs text-gray-400">
              Controls the maximum number of tokens the model can generate for vision analysis.
            </p>
          </div>
        </div>

        {visionConfig?.providerId === 'openrouter' && (
          <div className="p-3 bg-purple-900/20 border border-purple-700/30 rounded-lg">
            <div className="text-sm text-purple-300 font-medium mb-1">👁️ Vision Models</div>
            <div className="text-xs text-purple-200">
              Access to multimodal models from multiple providers for image analysis and vision tasks.
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderRoleAgentConfig = () => {
    const roleConfig = config as RoleAgentNodeData['config'];

    // Combine predefined and custom roles for dropdown
    const availableRoles = [
      ...PREDEFINED_ROLES.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description,
        type: 'predefined' as const
      })),
      ...customRoles.map(role => ({
        id: role.role_id,
        name: role.name,
        description: role.description,
        type: 'custom' as const
      }))
    ];

    const handleRoleSelectionChange = (value: string) => {
      if (value === 'create_new') {
        // Switch to create new role mode
        const newConfig = {
          ...roleConfig,
          roleType: 'new' as const,
          roleId: '',
          roleName: '',
          newRoleName: '',
          newRoleDescription: '',
          customPrompt: ''
        };
        setConfig(newConfig);
        onUpdate({
          config: newConfig,
          isConfigured: isNodeConfigured(node.type, newConfig)
        });
      } else {
        // Select existing role
        const selectedRole = availableRoles.find(role => role.id === value);
        if (selectedRole) {
          const newConfig = {
            ...roleConfig,
            roleType: selectedRole.type,
            roleId: selectedRole.id,
            roleName: selectedRole.name,
            customPrompt: selectedRole.description || ''
          };
          setConfig(newConfig);
          onUpdate({
            config: newConfig,
            isConfigured: isNodeConfigured(node.type, newConfig)
          });
        }
      }
    };

    const handleNewRoleChange = (field: string, value: string) => {
      const newConfig = {
        ...roleConfig,
        [field]: value
      };
      setConfig(newConfig);
      onUpdate({
        config: newConfig,
        isConfigured: isNodeConfigured(node.type, newConfig)
      });
    };

    return (
      <div className="space-y-4">
        {/* Role Selection Dropdown */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Select Role
          </label>
          {isLoadingRoles ? (
            <div className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400">
              Loading roles...
            </div>
          ) : (
            <select
              value={roleConfig?.roleType === 'new' ? 'create_new' : roleConfig?.roleId || ''}
              onChange={(e) => handleRoleSelectionChange(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
            >
              <option value="">Select a role...</option>

              {/* Predefined Roles */}
              <optgroup label="System Roles">
                {PREDEFINED_ROLES.map(role => (
                  <option key={role.id} value={role.id}>
                    {role.name}
                  </option>
                ))}
              </optgroup>

              {/* Custom Roles */}
              {customRoles.length > 0 && (
                <optgroup label="Your Custom Roles">
                  {customRoles.map(role => (
                    <option key={role.role_id} value={role.role_id}>
                      {role.name}
                    </option>
                  ))}
                </optgroup>
              )}

              {/* Create New Option */}
              <optgroup label="Create New">
                <option value="create_new">+ Create New Role</option>
              </optgroup>
            </select>
          )}

          {rolesError && (
            <p className="mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg">
              Error loading roles: {rolesError}
            </p>
          )}
        </div>

        {/* Show role description for selected role */}
        {roleConfig?.roleType !== 'new' && roleConfig?.roleId && (
          <div className="p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg">
            <div className="text-sm font-medium text-white mb-1">
              {roleConfig.roleName}
            </div>
            {roleConfig.customPrompt && (
              <div className="text-xs text-gray-300">
                {roleConfig.customPrompt}
              </div>
            )}
          </div>
        )}

        {/* Create New Role Fields */}
        {roleConfig?.roleType === 'new' && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                New Role Name
              </label>
              <input
                type="text"
                value={roleConfig.newRoleName || ''}
                onChange={(e) => handleNewRoleChange('newRoleName', e.target.value)}
                placeholder="e.g., Data Analyst, Creative Writer"
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Role Description
              </label>
              <input
                type="text"
                value={roleConfig.newRoleDescription || ''}
                onChange={(e) => handleNewRoleChange('newRoleDescription', e.target.value)}
                placeholder="Brief description of this role's purpose"
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Custom Instructions
              </label>
              <textarea
                value={roleConfig.customPrompt || ''}
                onChange={(e) => handleNewRoleChange('customPrompt', e.target.value)}
                placeholder="Enter detailed instructions for this role..."
                rows={4}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
              />
            </div>
          </>
        )}

        {/* Memory Toggle */}
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={roleConfig?.memoryEnabled || false}
              onChange={(e) => handleConfigChange('memoryEnabled', e.target.checked)}
              className="rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"
            />
            <span className="ml-2 text-sm text-gray-300">Enable memory</span>
          </label>
          <p className="text-xs text-gray-400 mt-1 ml-6">
            Allow this role to remember context from previous interactions
          </p>
        </div>
      </div>
    );
  };

  const renderConditionalConfig = () => {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Condition Type
          </label>
          <select
            value={config.conditionType || ''}
            onChange={(e) => handleConfigChange('conditionType', e.target.value)}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
          >
            <option value="">Select Type</option>
            <option value="contains">Contains</option>
            <option value="equals">Equals</option>
            <option value="regex">Regex</option>
            <option value="length">Length</option>
            <option value="custom">Custom</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Condition
          </label>
          <input
            type="text"
            value={config.condition || ''}
            onChange={(e) => handleConfigChange('condition', e.target.value)}
            placeholder="Enter condition..."
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              True Label
            </label>
            <input
              type="text"
              value={config.trueLabel || ''}
              onChange={(e) => handleConfigChange('trueLabel', e.target.value)}
              placeholder="Continue"
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              False Label
            </label>
            <input
              type="text"
              value={config.falseLabel || ''}
              onChange={(e) => handleConfigChange('falseLabel', e.target.value)}
              placeholder="Skip"
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
            />
          </div>
        </div>
      </div>
    );
  };

  const renderDefaultConfig = () => {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Label
          </label>
          <input
            type="text"
            value={node.data.label}
            onChange={(e) => onUpdate({ label: e.target.value })}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Description
          </label>
          <textarea
            value={node.data.description || ''}
            onChange={(e) => onUpdate({ description: e.target.value })}
            rows={3}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
          />
        </div>
      </div>
    );
  };

  const renderCentralRouterConfig = () => {
    const routerConfig = config as CentralRouterNodeData['config'];

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Routing Strategy
          </label>
          <select
            value={routerConfig?.routingStrategy || 'smart'}
            onChange={(e) => {
              const newConfig = {
                ...routerConfig,
                routingStrategy: e.target.value as any
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
          >
            <option value="smart">Smart Routing</option>
            <option value="round_robin">Round Robin</option>
            <option value="load_balanced">Load Balanced</option>
            <option value="priority">Priority Based</option>
          </select>
          <p className="text-xs text-gray-400 mt-1">
            How the router selects between available AI providers
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Max Retries
          </label>
          <input
            type="number"
            min="0"
            max="10"
            value={routerConfig?.maxRetries || 3}
            onChange={(e) => {
              const newConfig = {
                ...routerConfig,
                maxRetries: parseInt(e.target.value) || 3
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
          />
          <p className="text-xs text-gray-400 mt-1">
            Number of retry attempts on failure
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Timeout (ms)
          </label>
          <input
            type="number"
            min="1000"
            max="300000"
            step="1000"
            value={routerConfig?.timeout || 30000}
            onChange={(e) => {
              const newConfig = {
                ...routerConfig,
                timeout: parseInt(e.target.value) || 30000
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
          />
          <p className="text-xs text-gray-400 mt-1">
            Request timeout in milliseconds
          </p>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-300">
              Enable Caching
            </label>
            <input
              type="checkbox"
              checked={routerConfig?.enableCaching ?? true}
              onChange={(e) => {
                const newConfig = {
                  ...routerConfig,
                  enableCaching: e.target.checked
                };
                setConfig(newConfig);
                onUpdate({
                  config: newConfig,
                  isConfigured: isNodeConfigured(node.type, newConfig)
                });
              }}
              className="w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2"
            />
          </div>
          <p className="text-xs text-gray-400">
            Cache responses to improve performance
          </p>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-300">
              Debug Mode
            </label>
            <input
              type="checkbox"
              checked={routerConfig?.debugMode ?? false}
              onChange={(e) => {
                const newConfig = {
                  ...routerConfig,
                  debugMode: e.target.checked
                };
                setConfig(newConfig);
                onUpdate({
                  config: newConfig,
                  isConfigured: isNodeConfigured(node.type, newConfig)
                });
              }}
              className="w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2"
            />
          </div>
          <p className="text-xs text-gray-400">
            Enable detailed logging for debugging
          </p>
        </div>
      </div>
    );
  };

  const renderToolConfig = () => {
    const toolConfig = config as ToolNodeData['config'];

    const toolOptions = [
      { value: '', label: 'Select a tool...' },
      { value: 'google_drive', label: '📁 Google Drive', description: 'Access and manage Google Drive files' },
      { value: 'google_docs', label: '📄 Google Docs', description: 'Create and edit Google Documents' },
      { value: 'google_sheets', label: '📊 Google Sheets', description: 'Work with Google Spreadsheets' },
      { value: 'zapier', label: '⚡ Zapier', description: 'Connect with 5000+ apps via Zapier' },
      { value: 'notion', label: '📝 Notion', description: 'Access Notion databases and pages' },
      { value: 'calendar', label: '📅 Calendar', description: 'Manage calendar events and schedules' },
      { value: 'gmail', label: '📧 Gmail', description: 'Send and manage emails' },
      { value: 'youtube', label: '📺 YouTube', description: 'Access YouTube data and analytics' },
      { value: 'supabase', label: '🗄️ Supabase', description: 'Direct database operations' }
    ];

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Tool Type
          </label>
          <select
            value={toolConfig?.toolType || ''}
            onChange={(e) => {
              const newConfig = {
                ...toolConfig,
                toolType: e.target.value as any,
                // Reset tool-specific config when changing tool type
                toolConfig: {},
                // All tools need authentication
                connectionStatus: 'disconnected',
                isAuthenticated: false
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
          >
            {toolOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {toolConfig?.toolType && (
            <p className="text-xs text-gray-400 mt-1">
              {toolOptions.find(opt => opt.value === toolConfig.toolType)?.description}
            </p>
          )}
        </div>

        {/* Tools Configuration (Coming Soon) */}
        {toolConfig?.toolType && (
          <div className="space-y-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-yellow-400">●</span>
              <span className="text-sm font-medium text-yellow-400">Authentication Required</span>
            </div>

            <div className="text-center py-4">
              <p className="text-sm text-gray-400 mb-2">
                {toolOptions.find(opt => opt.value === toolConfig.toolType)?.label} integration coming soon!
              </p>
              <p className="text-xs text-gray-500">
                This tool will require account linking and authentication.
              </p>
            </div>
          </div>
        )}

        {/* Timeout Configuration */}
        {toolConfig?.toolType && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Timeout (seconds)
            </label>
            <input
              type="number"
              min="5"
              max="300"
              value={toolConfig?.timeout || 30}
              onChange={(e) => {
                const newConfig = {
                  ...toolConfig,
                  timeout: parseInt(e.target.value) || 30
                };
                setConfig(newConfig);
                onUpdate({
                  config: newConfig,
                  isConfigured: isNodeConfigured(node.type, newConfig)
                });
              }}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
            />
            <p className="text-xs text-gray-400 mt-1">
              Maximum time to wait for the tool operation to complete
            </p>
          </div>
        )}
      </div>
    );
  };

  const renderPlannerConfig = () => {
    const plannerConfig = config as PlannerNodeData['config'];

    // Get available models for the selected provider
    const availableModels = useMemo(() => {
      if (!plannerConfig?.providerId || !fetchedProviderModels) return [];

      return fetchedProviderModels
        .filter(model => model.provider_id === plannerConfig.providerId)
        .map(model => ({
          value: model.id,
          label: model.display_name || model.name,
          model: model
        }));
    }, [plannerConfig?.providerId, fetchedProviderModels]);

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Provider
          </label>
          <select
            value={plannerConfig?.providerId || ''}
            onChange={(e) => {
              const currentConfig = config as PlannerNodeData['config'];
              const newConfig = {
                ...currentConfig,
                providerId: e.target.value as any,
                modelId: '', // Reset model when provider changes
                parameters: currentConfig.parameters || {
                  temperature: 0.7,
                  maxTokens: undefined,
                  topP: undefined,
                  frequencyPenalty: undefined,
                  presencePenalty: undefined,
                }
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
          >
            <option value="">Select Provider</option>
            <option value="openai">OpenAI</option>
            <option value="anthropic">Anthropic</option>
            <option value="google">Google</option>
            <option value="deepseek">DeepSeek</option>
            <option value="xai">xAI</option>
            <option value="openrouter">OpenRouter</option>
          </select>
        </div>

        {plannerConfig?.providerId && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Model
            </label>
            <select
              value={plannerConfig?.modelId || ''}
              onChange={(e) => {
                const selectedModelId = e.target.value;
                let updatedConfig = {
                  ...plannerConfig,
                  modelId: selectedModelId
                };

                // Set reasonable default for maxTokens based on model limits
                if (selectedModelId && fetchedProviderModels) {
                  const selectedModel = fetchedProviderModels.find(m => m.id === selectedModelId);
                  if (selectedModel) {
                    const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;
                    const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));

                    const currentParams = plannerConfig?.parameters || {};
                    updatedConfig = {
                      ...updatedConfig,
                      parameters: {
                        ...currentParams,
                        maxTokens: reasonableDefault
                      }
                    };
                  }
                }

                setConfig(updatedConfig);
                onUpdate({
                  config: updatedConfig,
                  isConfigured: isNodeConfigured(node.type, updatedConfig)
                });
              }}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
            >
              <option value="">Select Model</option>
              {availableModels.map(model => (
                <option key={model.value} value={model.value}>
                  {model.label}
                </option>
              ))}
            </select>
            {isFetchingProviderModels && (
              <p className="text-xs text-gray-400 mt-1">Loading models...</p>
            )}
            {fetchProviderModelsError && (
              <p className="text-xs text-red-400 mt-1">{fetchProviderModelsError}</p>
            )}
          </div>
        )}

        {plannerConfig?.modelId && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              API Key (Optional)
            </label>
            <input
              type="password"
              value={plannerConfig?.apiKey || ''}
              onChange={(e) => {
                const newConfig = {
                  ...plannerConfig,
                  apiKey: e.target.value
                };
                setConfig(newConfig);
                onUpdate({
                  config: newConfig,
                  isConfigured: isNodeConfigured(node.type, newConfig)
                });
              }}
              placeholder="Enter API key or leave empty to use config keys"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
            />
            <p className="text-xs text-gray-400 mt-1">
              Leave empty to use keys from My Models configuration
            </p>
          </div>
        )}

        {plannerConfig?.modelId && (
          <>
            {/* Max Tokens Slider */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Max Tokens: {plannerConfig?.parameters?.maxTokens || 'Auto'}
              </label>
              <input
                type="range"
                min={tokenLimits.minTokens}
                max={tokenLimits.maxTokens}
                value={plannerConfig?.parameters?.maxTokens || tokenLimits.maxTokens}
                onChange={(e) => {
                  const newConfig = {
                    ...plannerConfig,
                    parameters: {
                      ...plannerConfig.parameters,
                      maxTokens: parseInt(e.target.value)
                    }
                  };
                  setConfig(newConfig);
                  onUpdate({
                    config: newConfig,
                    isConfigured: isNodeConfigured(node.type, newConfig)
                  });
                }}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-gray-400 mt-1">
                <span>{tokenLimits.minTokens}</span>
                <span>{tokenLimits.maxTokens}</span>
              </div>
            </div>

            {/* Temperature */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Temperature: {plannerConfig?.parameters?.temperature || 0.7}
              </label>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={plannerConfig?.parameters?.temperature || 0.7}
                onChange={(e) => {
                  const newConfig = {
                    ...plannerConfig,
                    parameters: {
                      ...plannerConfig.parameters,
                      temperature: parseFloat(e.target.value)
                    }
                  };
                  setConfig(newConfig);
                  onUpdate({
                    config: newConfig,
                    isConfigured: isNodeConfigured(node.type, newConfig)
                  });
                }}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-gray-400 mt-1">
                <span>0 (Focused)</span>
                <span>2 (Creative)</span>
              </div>
            </div>

            {/* Planning Prompt */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Planning Prompt (Optional)
              </label>
              <textarea
                value={plannerConfig?.planningPrompt || ''}
                onChange={(e) => {
                  const newConfig = {
                    ...plannerConfig,
                    planningPrompt: e.target.value
                  };
                  setConfig(newConfig);
                  onUpdate({
                    config: newConfig,
                    isConfigured: isNodeConfigured(node.type, newConfig)
                  });
                }}
                placeholder="Custom prompt for planning browsing tasks..."
                rows={3}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent resize-none"
              />
              <p className="text-xs text-gray-400 mt-1">
                Custom instructions for how the AI should plan browsing tasks
              </p>
            </div>
          </>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Max Subtasks
          </label>
          <input
            type="number"
            min="1"
            max="50"
            value={plannerConfig?.maxSubtasks || 10}
            onChange={(e) => {
              const newConfig = {
                ...plannerConfig,
                maxSubtasks: parseInt(e.target.value) || 10
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
          />
          <p className="text-xs text-gray-400 mt-1">
            Maximum number of subtasks the planner can create
          </p>
        </div>
      </div>
    );
  };

  const renderBrowsingConfig = () => {
    const browsingConfig = config as BrowsingNodeData['config'];

    return (
      <div className="space-y-4">
        <div className="p-4 bg-green-900/20 border border-green-700 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-green-400">●</span>
            <span className="text-sm font-medium text-green-400">Intelligent Browsing Agent</span>
          </div>
          <p className="text-xs text-gray-300">
            This node automatically plans and executes complex web browsing tasks using AI.
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Max Sites to Visit
          </label>
          <input
            type="number"
            min="1"
            max="20"
            value={browsingConfig?.maxSites || 5}
            onChange={(e) => {
              const newConfig = {
                ...browsingConfig,
                maxSites: parseInt(e.target.value) || 5
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Timeout per Operation (seconds)
          </label>
          <input
            type="number"
            min="10"
            max="300"
            value={browsingConfig?.timeout || 30}
            onChange={(e) => {
              const newConfig = {
                ...browsingConfig,
                timeout: parseInt(e.target.value) || 30
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
          />
        </div>

        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-300">
            Capabilities
          </label>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={browsingConfig?.enableScreenshots ?? true}
              onChange={(e) => {
                const newConfig = {
                  ...browsingConfig,
                  enableScreenshots: e.target.checked
                };
                setConfig(newConfig);
                onUpdate({
                  config: newConfig,
                  isConfigured: isNodeConfigured(node.type, newConfig)
                });
              }}
              className="rounded"
            />
            <span className="text-sm text-gray-300">📸 Take Screenshots</span>
          </div>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={browsingConfig?.enableFormFilling ?? true}
              onChange={(e) => {
                const newConfig = {
                  ...browsingConfig,
                  enableFormFilling: e.target.checked
                };
                setConfig(newConfig);
                onUpdate({
                  config: newConfig,
                  isConfigured: isNodeConfigured(node.type, newConfig)
                });
              }}
              className="rounded"
            />
            <span className="text-sm text-gray-300">📝 Fill Forms Automatically</span>
          </div>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={browsingConfig?.enableCaptchaSolving ?? false}
              onChange={(e) => {
                const newConfig = {
                  ...browsingConfig,
                  enableCaptchaSolving: e.target.checked
                };
                setConfig(newConfig);
                onUpdate({
                  config: newConfig,
                  isConfigured: isNodeConfigured(node.type, newConfig)
                });
              }}
              className="rounded"
            />
            <span className="text-sm text-gray-300">🔐 Attempt CAPTCHA Solving</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Search Engines
          </label>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={browsingConfig?.searchEngines?.includes('google') ?? true}
                onChange={(e) => {
                  const currentEngines = browsingConfig?.searchEngines || ['google'];
                  const newEngines = e.target.checked
                    ? [...currentEngines.filter(eng => eng !== 'google'), 'google']
                    : currentEngines.filter(eng => eng !== 'google');

                  const newConfig = {
                    ...browsingConfig,
                    searchEngines: newEngines.length > 0 ? newEngines : ['google']
                  };
                  setConfig(newConfig);
                  onUpdate({
                    config: newConfig,
                    isConfigured: isNodeConfigured(node.type, newConfig)
                  });
                }}
                className="rounded"
              />
              <span className="text-sm text-gray-300">Google</span>
            </div>
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={browsingConfig?.searchEngines?.includes('bing') ?? false}
                onChange={(e) => {
                  const currentEngines = browsingConfig?.searchEngines || ['google'];
                  const newEngines = e.target.checked
                    ? [...currentEngines.filter(eng => eng !== 'bing'), 'bing']
                    : currentEngines.filter(eng => eng !== 'bing');

                  const newConfig = {
                    ...browsingConfig,
                    searchEngines: newEngines.length > 0 ? newEngines : ['google']
                  };
                  setConfig(newConfig);
                  onUpdate({
                    config: newConfig,
                    isConfigured: isNodeConfigured(node.type, newConfig)
                  });
                }}
                className="rounded"
              />
              <span className="text-sm text-gray-300">Bing</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderMemoryConfig = () => {
    const memoryConfig = config as MemoryNodeData['config'];

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Memory Type
          </label>
          <select
            value={memoryConfig?.memoryType || ''}
            onChange={(e) => {
              const newConfig = {
                ...memoryConfig,
                memoryType: e.target.value as 'store' | 'retrieve' | 'session' | 'persistent'
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
          >
            <option value="">Select Memory Type</option>
            <option value="store">Store Data</option>
            <option value="retrieve">Retrieve Data</option>
            <option value="session">Session Memory</option>
            <option value="persistent">Persistent Memory</option>
          </select>
          <p className="text-xs text-gray-400 mt-1">
            Choose how this memory node will handle data
          </p>
        </div>

        {memoryConfig?.memoryType && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Storage Key
            </label>
            <input
              type="text"
              value={memoryConfig?.storageKey || ''}
              onChange={(e) => {
                const newConfig = {
                  ...memoryConfig,
                  storageKey: e.target.value
                };
                setConfig(newConfig);
                onUpdate({
                  config: newConfig,
                  isConfigured: isNodeConfigured(node.type, newConfig)
                });
              }}
              placeholder="Enter unique storage key (e.g., user_preferences, task_history)"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
            />
            <p className="text-xs text-gray-400 mt-1">
              Unique identifier for this memory storage
            </p>
          </div>
        )}

        {memoryConfig?.storageKey && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Storage Scope
              </label>
              <select
                value={memoryConfig?.storageScope || 'workflow'}
                onChange={(e) => {
                  const newConfig = {
                    ...memoryConfig,
                    storageScope: e.target.value as 'workflow' | 'user' | 'global'
                  };
                  setConfig(newConfig);
                  onUpdate({
                    config: newConfig,
                    isConfigured: isNodeConfigured(node.type, newConfig)
                  });
                }}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
              >
                <option value="workflow">Workflow Scope</option>
                <option value="user">User Scope</option>
                <option value="global">Global Scope</option>
              </select>
              <p className="text-xs text-gray-400 mt-1">
                Determines who can access this memory data
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Data Format
              </label>
              <select
                value={memoryConfig?.dataFormat || 'text'}
                onChange={(e) => {
                  const newConfig = {
                    ...memoryConfig,
                    dataFormat: e.target.value as 'text' | 'json' | 'structured'
                  };
                  setConfig(newConfig);
                  onUpdate({
                    config: newConfig,
                    isConfigured: isNodeConfigured(node.type, newConfig)
                  });
                }}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
              >
                <option value="text">Plain Text</option>
                <option value="json">JSON Object</option>
                <option value="structured">Structured Data</option>
              </select>
              <p className="text-xs text-gray-400 mt-1">
                Format for storing and retrieving data
              </p>
            </div>

            {(memoryConfig?.memoryType === 'session' || memoryConfig?.memoryType === 'persistent') && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Max Storage Size (KB)
                </label>
                <input
                  type="number"
                  min="1"
                  max="10240"
                  value={memoryConfig?.maxSize || 1024}
                  onChange={(e) => {
                    const newConfig = {
                      ...memoryConfig,
                      maxSize: parseInt(e.target.value) || 1024
                    };
                    setConfig(newConfig);
                    onUpdate({
                      config: newConfig,
                      isConfigured: isNodeConfigured(node.type, newConfig)
                    });
                  }}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Maximum storage size limit in kilobytes
                </p>
              </div>
            )}

            {memoryConfig?.memoryType === 'session' && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Time to Live (seconds)
                </label>
                <input
                  type="number"
                  min="60"
                  max="86400"
                  value={memoryConfig?.ttl || 3600}
                  onChange={(e) => {
                    const newConfig = {
                      ...memoryConfig,
                      ttl: parseInt(e.target.value) || 3600
                    };
                    setConfig(newConfig);
                    onUpdate({
                      config: newConfig,
                      isConfigured: isNodeConfigured(node.type, newConfig)
                    });
                  }}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
                />
                <p className="text-xs text-gray-400 mt-1">
                  How long to keep session data (1 hour = 3600 seconds)
                </p>
              </div>
            )}

            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={memoryConfig?.encryption || false}
                  onChange={(e) => {
                    const newConfig = {
                      ...memoryConfig,
                      encryption: e.target.checked
                    };
                    setConfig(newConfig);
                    onUpdate({
                      config: newConfig,
                      isConfigured: isNodeConfigured(node.type, newConfig)
                    });
                  }}
                  className="rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"
                />
                <span className="ml-2 text-sm text-gray-300">Enable encryption</span>
              </label>
              <p className="text-xs text-gray-400 mt-1 ml-6">
                Encrypt stored data for additional security
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Description (Optional)
              </label>
              <textarea
                value={memoryConfig?.description || ''}
                onChange={(e) => {
                  const newConfig = {
                    ...memoryConfig,
                    description: e.target.value
                  };
                  setConfig(newConfig);
                  onUpdate({
                    config: newConfig,
                    isConfigured: isNodeConfigured(node.type, newConfig)
                  });
                }}
                placeholder="Describe what this memory stores..."
                rows={2}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent resize-none"
              />
              <p className="text-xs text-gray-400 mt-1">
                Optional description of what this memory node stores
              </p>
            </div>
          </>
        )}
      </div>
    );
  };

  const renderConfigContent = () => {
    switch (node.type) {
      case 'provider':
        return renderProviderConfig();
      case 'vision':
        return renderVisionConfig();
      case 'roleAgent':
        return renderRoleAgentConfig();
      case 'centralRouter':
        return renderCentralRouterConfig();
      case 'conditional':
        return renderConditionalConfig();
      case 'tool':
        return renderToolConfig();
      case 'planner':
        return renderPlannerConfig();
      case 'browsing':
        return renderBrowsingConfig();
      case 'memory':
        return renderMemoryConfig();
      default:
        return renderDefaultConfig();
    }
  };

  return (
    <div className="w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-[#ff6b35]/20 rounded-lg">
            <Cog6ToothIcon className="w-5 h-5 text-[#ff6b35]" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">
              Configure Node
            </h3>
            <p className="text-sm text-gray-400">
              {node.data.label}
            </p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white transition-colors p-1 rounded"
        >
          <XMarkIcon className="w-5 h-5" />
        </button>
      </div>

      {/* Configuration Form */}
      <div className="space-y-6">
        {renderConfigContent()}
      </div>

      {/* Status */}
      <div className="mt-6 p-3 rounded-lg border border-gray-700/50">
        <div className="flex items-center gap-2 mb-2">
          <div className={`w-2 h-2 rounded-full ${
            node.data.isConfigured ? 'bg-green-500' : 'bg-yellow-500'
          }`} />
          <span className="text-sm font-medium text-white">
            {node.data.isConfigured ? 'Configured' : 'Needs Configuration'}
          </span>
        </div>
        <p className="text-xs text-gray-400">
          {node.data.isConfigured 
            ? 'This node is properly configured and ready to use.'
            : 'Complete the configuration to use this node in your workflow.'
          }
        </p>
      </div>
    </div>
  );
}
