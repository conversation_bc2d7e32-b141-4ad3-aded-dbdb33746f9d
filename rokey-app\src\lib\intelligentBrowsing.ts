// Intelligent Browsing Service
// Handles complex multi-step browsing tasks with AI planning and memory

import BrowserlessService from './browserless';

export interface BrowsingPlan {
  id: string;
  task: string;
  subtasks: BrowsingSubtask[];
  estimatedTime: number;
  priority: 'low' | 'medium' | 'high';
}

export interface BrowsingSubtask {
  id: string;
  type: 'search' | 'navigate' | 'extract' | 'screenshot' | 'form_fill' | 'click' | 'wait' | 'analyze_snippets' | 'check_completion';
  description: string;
  target?: string; // URL, search query, selector, etc.
  parameters?: Record<string, any>;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
  result?: any;
  error?: string;
  executionTime?: number;
  priority?: 'low' | 'medium' | 'high'; // For smart selection
}

export interface BrowsingMemory {
  taskId: string;
  completedSubtasks: string[];
  gatheredData: Record<string, any>;
  visitedUrls: string[];
  searchQueries: string[];
  screenshots: string[];
  searchResults: any[]; // Store search snippets for analysis
  selectedWebsites: string[]; // URLs selected based on snippet analysis
  completionStatus: 'insufficient' | 'partial' | 'sufficient' | 'complete';
  lastUpdate: string;
  isComplete: boolean;
  finalResult?: any;
}

export interface BrowsingConfig {
  maxSites?: number;
  timeout?: number;
  enableScreenshots?: boolean;
  enableFormFilling?: boolean;
  enableCaptchaSolving?: boolean;
  searchEngines?: ('google' | 'bing')[];
  maxDepth?: number;
  respectRobots?: boolean;
  userAgent?: string;
  enableJavaScript?: boolean;
}

export class IntelligentBrowsingService {
  private static instance: IntelligentBrowsingService;
  private browserless: BrowserlessService;
  private activeTasks: Map<string, BrowsingMemory> = new Map();

  constructor() {
    this.browserless = BrowserlessService.getInstance();
  }

  static getInstance(): IntelligentBrowsingService {
    if (!IntelligentBrowsingService.instance) {
      IntelligentBrowsingService.instance = new IntelligentBrowsingService();
    }
    return IntelligentBrowsingService.instance;
  }

  // Execute a browsing plan with memory tracking and smart completion detection
  async executeBrowsingPlan(
    plan: BrowsingPlan,
    memory: BrowsingMemory,
    config: BrowsingConfig = {}
  ): Promise<{ memory: BrowsingMemory; result: any }> {
    console.log(`Starting browsing task: ${plan.task}`);

    // Update memory with task start
    memory.lastUpdate = new Date().toISOString();
    memory.completionStatus = 'insufficient';
    this.activeTasks.set(plan.id, memory);

    let finalResult = null;
    const results: any[] = [];

    try {
      for (const subtask of plan.subtasks) {
        // Skip if already completed
        if (memory.completedSubtasks.includes(subtask.id)) {
          console.log(`Skipping completed subtask: ${subtask.description}`);
          continue;
        }

        // Check if we have sufficient information to complete the task early
        if (await this.checkTaskCompletion(memory, plan.task)) {
          console.log(`✅ Task completion detected early. Skipping remaining subtasks.`);
          memory.completionStatus = 'complete';

          // Mark remaining subtasks as skipped
          const remainingSubtasks = plan.subtasks.filter(st =>
            !memory.completedSubtasks.includes(st.id) && st.id !== subtask.id
          );
          remainingSubtasks.forEach(st => st.status = 'skipped');

          break;
        }

        console.log(`Executing subtask: ${subtask.description}`);
        subtask.status = 'in_progress';

        const startTime = Date.now();

        try {
          const result = await this.executeSubtask(subtask, memory, config);

          subtask.status = 'completed';
          subtask.result = result;
          subtask.executionTime = Date.now() - startTime;

          // Update memory
          memory.completedSubtasks.push(subtask.id);
          memory.gatheredData[subtask.id] = result;
          memory.lastUpdate = new Date().toISOString();

          // Update completion status based on gathered data
          memory.completionStatus = this.assessCompletionStatus(memory, plan.task);

          results.push(result);

          console.log(`Completed subtask: ${subtask.description} (Status: ${memory.completionStatus})`);

        } catch (error) {
          subtask.status = 'failed';
          subtask.error = error instanceof Error ? error.message : 'Unknown error';
          subtask.executionTime = Date.now() - startTime;

          console.error(`Failed subtask: ${subtask.description}`, error);

          // Decide whether to continue or abort based on subtask importance
          if (subtask.type === 'search' || subtask.type === 'navigate') {
            // Critical subtasks - abort if they fail
            throw error;
          }
          // Non-critical subtasks - continue with warning
        }
      }

      // Synthesize final result from all gathered data
      finalResult = this.synthesizeResults(results, plan.task);
      
      memory.isComplete = true;
      memory.finalResult = finalResult;
      memory.lastUpdate = new Date().toISOString();
      
      console.log(`Completed browsing task: ${plan.task}`);
      
    } catch (error) {
      console.error(`Browsing task failed: ${plan.task}`, error);
      memory.lastUpdate = new Date().toISOString();
      throw error;
    } finally {
      this.activeTasks.set(plan.id, memory);
    }

    return { memory, result: finalResult };
  }

  // Execute individual subtask
  private async executeSubtask(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    switch (subtask.type) {
      case 'search':
        return this.executeSearch(subtask, memory, config);
      case 'navigate':
        return this.executeNavigate(subtask, memory, config);
      case 'extract':
        return this.executeExtract(subtask, memory, config);
      case 'screenshot':
        return this.executeScreenshot(subtask, memory, config);
      case 'form_fill':
        return this.executeFormFill(subtask, memory, config);
      case 'click':
        return this.executeClick(subtask, memory, config);
      case 'wait':
        return this.executeWait(subtask, memory, config);
      case 'analyze_snippets':
        return this.executeSnippetAnalysis(subtask, memory, config);
      case 'check_completion':
        return this.executeCompletionCheck(subtask, memory, config);
      default:
        throw new Error(`Unknown subtask type: ${subtask.type}`);
    }
  }

  private async executeSearch(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    const query = subtask.target || '';
    const searchEngine = config.searchEngines?.[0] || 'google';

    memory.searchQueries.push(query);

    const result = await this.browserless.searchAndExtract(query, searchEngine);

    // Store search results with snippets for analysis
    if (result.data && Array.isArray(result.data)) {
      memory.searchResults.push(...result.data.map((item: any) => ({
        ...item,
        query,
        searchEngine,
        timestamp: new Date().toISOString()
      })));
    }

    return result.data;
  }

  private async executeNavigate(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    const url = subtask.target || '';
    const selector = subtask.parameters?.selector;
    
    memory.visitedUrls.push(url);
    
    const result = await this.browserless.navigateAndExtract(url, selector);
    return result.data;
  }

  private async executeExtract(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    // Custom extraction logic using Browserless function
    const url = subtask.target || '';
    const selector = subtask.parameters?.selector || 'body';
    
    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });
        
        const elements = await page.$$eval("${selector}", els => 
          els.map(el => ({
            text: el.textContent?.trim() || '',
            html: el.innerHTML,
            attributes: Object.fromEntries(
              Array.from(el.attributes).map(attr => [attr.name, attr.value])
            )
          }))
        );
        
        return {
          data: {
            url: "${url}",
            selector: "${selector}",
            elements,
            extractedAt: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    const result = await this.browserless.executeFunction(code);
    return result.data;
  }

  private async executeScreenshot(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    const url = subtask.target || '';
    
    const result = await this.browserless.takeScreenshot(url);
    
    if (result.data.screenshot) {
      memory.screenshots.push(result.data.screenshot);
    }
    
    return result.data;
  }

  private async executeFormFill(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    // Form filling logic - to be implemented
    throw new Error('Form filling not yet implemented');
  }

  private async executeClick(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    // Click logic - to be implemented
    throw new Error('Click actions not yet implemented');
  }

  private async executeWait(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    const waitTime = subtask.parameters?.duration || 1000;
    await new Promise(resolve => setTimeout(resolve, waitTime));
    return { waited: waitTime };
  }

  // Synthesize final result from all gathered data
  private synthesizeResults(results: any[], originalTask: string): any {
    return {
      task: originalTask,
      summary: `Completed browsing task with ${results.length} operations`,
      data: results,
      synthesizedAt: new Date().toISOString(),
      totalOperations: results.length
    };
  }

  // Get memory for a task
  getTaskMemory(taskId: string): BrowsingMemory | undefined {
    return this.activeTasks.get(taskId);
  }

  // Smart snippet analysis to select best websites
  private async executeSnippetAnalysis(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    if (memory.searchResults.length === 0) {
      return { selectedWebsites: [], reasoning: 'No search results to analyze' };
    }

    // Analyze snippets and select best websites
    const analysisPrompt = `Analyze these search results and select the 3-5 most relevant websites based on their snippets and titles.

Task: ${subtask.parameters?.originalTask || 'Unknown task'}

Search Results:
${memory.searchResults.map((result, index) =>
  `${index + 1}. Title: ${result.title}
     URL: ${result.url}
     Snippet: ${result.snippet || result.description || 'No snippet available'}
     Source: ${result.query} (${result.searchEngine})`
).join('\n\n')}

Select the best websites that are most likely to contain the information needed. Consider:
- Relevance to the task
- Authority and reliability of the source
- Freshness of content
- Likelihood of containing specific data needed

Respond with JSON:
{
  "selectedUrls": ["url1", "url2", "url3"],
  "reasoning": "explanation of why these sites were chosen",
  "confidence": 0.85
}`;

    try {
      // This would call an AI service to analyze snippets
      // For now, implement a simple heuristic-based selection
      const selectedWebsites = this.selectWebsitesHeuristic(memory.searchResults, subtask);
      memory.selectedWebsites = selectedWebsites;

      return {
        selectedWebsites,
        reasoning: 'Selected based on relevance heuristics',
        confidence: 0.75
      };
    } catch (error) {
      console.error('Snippet analysis failed:', error);
      // Fallback: select first few results
      const fallbackSelection = memory.searchResults.slice(0, 3).map(r => r.url);
      memory.selectedWebsites = fallbackSelection;
      return {
        selectedWebsites: fallbackSelection,
        reasoning: 'Fallback selection due to analysis error',
        confidence: 0.5
      };
    }
  }

  // Heuristic-based website selection
  private selectWebsitesHeuristic(searchResults: any[], subtask: BrowsingSubtask): string[] {
    const scored = searchResults.map(result => {
      let score = 0;
      const title = (result.title || '').toLowerCase();
      const snippet = (result.snippet || result.description || '').toLowerCase();
      const url = (result.url || '').toLowerCase();

      // Boost official/authoritative sources
      if (url.includes('.gov') || url.includes('.edu') || url.includes('.org')) score += 20;

      // Boost well-known domains
      const knownDomains = ['wikipedia', 'amazon', 'apple', 'google', 'microsoft', 'github'];
      if (knownDomains.some(domain => url.includes(domain))) score += 15;

      // Boost if snippet contains relevant keywords
      const taskKeywords = (subtask.parameters?.keywords || []);
      taskKeywords.forEach((keyword: string) => {
        if (snippet.includes(keyword.toLowerCase())) score += 10;
        if (title.includes(keyword.toLowerCase())) score += 15;
      });

      // Penalize very long URLs (often less reliable)
      if (url.length > 100) score -= 5;

      // Boost if has good snippet (indicates content richness)
      if (snippet.length > 100) score += 5;

      return { ...result, score };
    });

    // Sort by score and take top 5
    return scored
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)
      .map(r => r.url);
  }

  // Check if task completion criteria are met
  private async executeCompletionCheck(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    const completionStatus = this.assessCompletionStatus(memory, subtask.parameters?.originalTask || '');

    return {
      status: completionStatus,
      canComplete: completionStatus === 'sufficient' || completionStatus === 'complete',
      dataPoints: Object.keys(memory.gatheredData).length,
      reasoning: this.getCompletionReasoning(memory, completionStatus)
    };
  }

  // Assess completion status based on gathered data
  private assessCompletionStatus(memory: BrowsingMemory, originalTask: string): 'insufficient' | 'partial' | 'sufficient' | 'complete' {
    const dataPoints = Object.keys(memory.gatheredData).length;
    const hasScreenshots = memory.screenshots.length > 0;
    const visitedSites = memory.visitedUrls.length;

    // Simple heuristic - can be enhanced with AI analysis
    if (dataPoints === 0) return 'insufficient';
    if (dataPoints < 3 && visitedSites < 2) return 'partial';
    if (dataPoints >= 3 && visitedSites >= 2) return 'sufficient';
    if (dataPoints >= 5 && hasScreenshots && visitedSites >= 3) return 'complete';

    return 'partial';
  }

  // Check if we have enough information to complete the task early
  private async checkTaskCompletion(memory: BrowsingMemory, originalTask: string): Promise<boolean> {
    const status = this.assessCompletionStatus(memory, originalTask);
    return status === 'sufficient' || status === 'complete';
  }

  // Get reasoning for completion status
  private getCompletionReasoning(memory: BrowsingMemory, status: string): string {
    const dataPoints = Object.keys(memory.gatheredData).length;
    const visitedSites = memory.visitedUrls.length;

    switch (status) {
      case 'insufficient':
        return `Need more data. Currently have ${dataPoints} data points from ${visitedSites} sites.`;
      case 'partial':
        return `Making progress. Have ${dataPoints} data points from ${visitedSites} sites, but need more comprehensive data.`;
      case 'sufficient':
        return `Have enough data to complete task. Collected ${dataPoints} data points from ${visitedSites} sites.`;
      case 'complete':
        return `Task fully complete. Comprehensive data collected: ${dataPoints} data points, ${memory.screenshots.length} screenshots, ${visitedSites} sites visited.`;
      default:
        return 'Status assessment in progress.';
    }
  }

  // Create new memory for a task
  createTaskMemory(taskId: string): BrowsingMemory {
    const memory: BrowsingMemory = {
      taskId,
      completedSubtasks: [],
      gatheredData: {},
      visitedUrls: [],
      searchQueries: [],
      screenshots: [],
      searchResults: [],
      selectedWebsites: [],
      completionStatus: 'insufficient',
      lastUpdate: new Date().toISOString(),
      isComplete: false
    };

    this.activeTasks.set(taskId, memory);
    return memory;
  }

  // Get service statistics
  getStats(): any {
    return {
      activeTasks: this.activeTasks.size,
      browserlessStats: this.browserless.getStats(),
      tasks: Array.from(this.activeTasks.values()).map(memory => ({
        taskId: memory.taskId,
        completedSubtasks: memory.completedSubtasks.length,
        isComplete: memory.isComplete,
        lastUpdate: memory.lastUpdate
      }))
    };
  }
}
