'use client';

import { CpuChipIcon } from '@heroicons/react/24/outline';
import { NodeProps, useEdges, useNodes, Handle, Position } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode, CentralRouterNodeData } from '@/types/manualBuild';

export default function CentralRouterNode({ data, id }: NodeProps<WorkflowNode['data']>) {
  const edges = useEdges();
  const nodes = useNodes();
  const config = data.config as CentralRouterNodeData['config'];
  
  // Get connected AI providers
  const connectedProviders = edges
    .filter(edge => edge.target === id && edge.targetHandle === 'providers')
    .map(edge => {
      const sourceNode = nodes.find(node => node.id === edge.source);
      if (sourceNode && sourceNode.type === 'provider') {
        const providerConfig = sourceNode.data.config as any;
        return {
          id: edge.source,
          name: providerConfig?.providerId || 'Unknown Provider',
          model: providerConfig?.modelId || 'Unknown Model'
        };
      }
      return null;
    })
    .filter(Boolean);

  // Get connected vision nodes
  const connectedVision = edges
    .filter(edge => edge.target === id && edge.targetHandle === 'vision')
    .map(edge => {
      const sourceNode = nodes.find(node => node.id === edge.source);
      if (sourceNode && sourceNode.type === 'vision') {
        const visionConfig = sourceNode.data.config as any;
        return {
          id: edge.source,
          name: visionConfig?.providerId || 'Unknown Vision Provider',
          model: visionConfig?.modelId || 'Unknown Model'
        };
      }
      return null;
    })
    .filter(Boolean);

  // Check if classifier is connected
  const hasClassifier = edges.some(edge => 
    edge.target === id && 
    edge.targetHandle === 'classifier' &&
    nodes.find(node => node.id === edge.source && node.type === 'classifier')
  );

  return (
    <div className="relative">
      {/* Multiple Input Handles */}
      
      {/* AI Providers Input */}
      <Handle
        type="target"
        position={Position.Left}
        id="providers"
        className="w-6 h-6 border-2 border-blue-500 bg-blue-700 hover:border-blue-400 hover:bg-blue-400 transition-colors"
        style={{ left: -12, top: '20%' }}
      />
      <div className="absolute text-xs text-blue-200 font-medium pointer-events-none" style={{ left: -80, top: '15%' }}>
        AI Providers
      </div>

      {/* Vision Input */}
      <Handle
        type="target"
        position={Position.Left}
        id="vision"
        className="w-6 h-6 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors"
        style={{ left: -12, top: '50%' }}
      />
      <div className="absolute text-xs text-purple-200 font-medium pointer-events-none" style={{ left: -80, top: '45%' }}>
        Vision Models
      </div>

      {/* Classifier Input */}
      <Handle
        type="target"
        position={Position.Left}
        id="classifier"
        className="w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors"
        style={{ left: -12, top: '65%' }}
      />
      <div className="absolute text-xs text-green-200 font-medium pointer-events-none" style={{ left: -80, top: '60%' }}>
        Classifier
      </div>

      {/* Memory Input */}
      <Handle
        type="target"
        position={Position.Left}
        id="memory"
        className="w-6 h-6 border-2 border-yellow-500 bg-yellow-700 hover:border-yellow-400 hover:bg-yellow-400 transition-colors"
        style={{ left: -12, top: '85%' }}
      />
      <div className="absolute text-xs text-yellow-200 font-medium pointer-events-none" style={{ left: -60, top: '80%' }}>
        Memory
      </div>

      {/* Data Input */}
      <Handle
        type="target"
        position={Position.Top}
        id="input"
        className="w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors"
        style={{ top: -12 }}
      />
      <div className="absolute text-xs text-gray-300 font-medium pointer-events-none" style={{ top: -30, left: '45%' }}>
        Input
      </div>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors"
        style={{ right: -12 }}
      />
      <div className="absolute text-xs text-orange-200 font-medium pointer-events-none" style={{ right: -60, top: '45%' }}>
        Output
      </div>

      {/* Node Body */}
      <div
        className={`min-w-[280px] rounded-lg border-2 transition-all duration-200 ${
          data.hasError
            ? 'border-red-500 bg-red-900/20'
            : data.isConfigured
            ? 'border-gray-600 bg-gray-800/90'
            : 'border-yellow-500 bg-yellow-900/20'
        } backdrop-blur-sm shadow-lg hover:shadow-xl`}
        style={{
          borderColor: data.hasError ? '#ef4444' : data.isConfigured ? '#ff6b35' : '#eab308'
        }}
      >
        {/* Header */}
        <div 
          className="px-4 py-3 rounded-t-lg flex items-center gap-3"
          style={{
            background: data.hasError 
              ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))'
              : 'linear-gradient(135deg, #ff6b3520, #ff6b3510)'
          }}
        >
          <div 
            className="p-2 rounded-lg"
            style={{
              backgroundColor: data.hasError ? '#ef444420' : '#ff6b3520',
              color: data.hasError ? '#ef4444' : '#ff6b35'
            }}
          >
            <CpuChipIcon className="w-4 h-4" />
          </div>
          <div className="flex-1">
            <div className="font-medium text-white text-sm">
              Central Router
            </div>
            <div className="text-xs text-gray-400 mt-1">
              Smart routing hub for AI providers
            </div>
          </div>
          
          {/* Status Indicators */}
          <div className="flex items-center gap-2">
            {hasClassifier && (
              <div className="w-2 h-2 bg-green-500 rounded-full" title="Classifier Connected" />
            )}
            {connectedProviders.length > 0 && (
              <div className="w-2 h-2 bg-blue-500 rounded-full" title={`${connectedProviders.length} AI Providers`} />
            )}
            {connectedVision.length > 0 && (
              <div className="w-2 h-2 bg-purple-500 rounded-full" title={`${connectedVision.length} Vision Models`} />
            )}
          </div>
        </div>

        {/* Content */}
        <div className="px-4 py-3 space-y-3">
          {/* Connected AI Providers */}
          {connectedProviders.length > 0 && (
            <div className="space-y-1">
              <div className="text-xs text-gray-400">AI Providers ({connectedProviders.length}):</div>
              <div className="flex flex-wrap gap-1">
                {connectedProviders.map((provider: any) => (
                  <span
                    key={provider.id}
                    className="text-xs bg-blue-900/30 text-blue-300 px-2 py-0.5 rounded-full border border-blue-700/30"
                  >
                    {provider.name}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Connected Vision Models */}
          {connectedVision.length > 0 && (
            <div className="space-y-1">
              <div className="text-xs text-gray-400">Vision Models ({connectedVision.length}):</div>
              <div className="flex flex-wrap gap-1">
                {connectedVision.map((vision: any) => (
                  <span
                    key={vision.id}
                    className="text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30"
                  >
                    {vision.name}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Routing Strategy */}
          {config?.routingStrategy && (
            <div className="text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded">
              Strategy: {config.routingStrategy.replace('_', ' ').toUpperCase()}
            </div>
          )}

          {/* Status Messages */}
          {!hasClassifier && (
            <div className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded">
              ⚠️ Connect classifier for smart routing
            </div>
          )}
          
          {connectedProviders.length === 0 && connectedVision.length === 0 && (
            <div className="text-xs text-red-300 bg-red-900/20 px-2 py-1 rounded">
              ❌ No AI providers connected
            </div>
          )}

          {(connectedProviders.length > 0 || connectedVision.length > 0) && hasClassifier && (
            <div className="text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded">
              ✅ Ready for smart routing
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
